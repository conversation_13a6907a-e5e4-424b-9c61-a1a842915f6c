<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>样式测试 - Bonk Game</title>
    <link rel="stylesheet" href="assets/css/optimized-styles.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 20px;
            color: var(--primary-orange, #ff6b35);
        }
    </style>
</head>
<body>
    <h1>Bonk Game 样式优化测试页面</h1>
    
    <div class="test-section">
        <div class="test-title">1. 颜色变量测试</div>
        <div style="background: var(--primary-orange); color: white; padding: 10px; border-radius: 4px;">
            主要橙色 (--primary-orange)
        </div>
        <div style="background: var(--deep-orange); color: white; padding: 10px; border-radius: 4px; margin-top: 10px;">
            深橙色 (--deep-orange)
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">2. 按钮样式测试</div>
        <button class="button primary">主要按钮</button>
        <button class="button secondary" style="margin-left: 10px;">次要按钮</button>
    </div>

    <div class="test-section">
        <div class="test-title">3. 徽章样式测试</div>
        <span class="badge">HOT</span>
        <span class="badge new" style="margin-left: 10px;">NEW</span>
    </div>

    <div class="test-section">
        <div class="test-title">4. 游戏卡片样式测试</div>
        <div class="tiles" style="max-width: 600px;">
            <article class="clickable-article">
                <div class="badge new">NEW</div>
                <span class="image">
                    <img src="https://via.placeholder.com/300x200/ff6b35/ffffff?text=Test+Game" alt="测试游戏">
                </span>
                <header class="major">
                    <h3><a href="#" class="link">测试游戏</a></h3>
                    <p>这是一个测试游戏卡片</p>
                    <div class="game-meta">
                        <span class="rating">⭐ 4.8</span>
                        <span class="genre">测试</span>
                    </div>
                </header>
            </article>
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">5. 响应式测试</div>
        <p>请调整浏览器窗口大小来测试响应式设计。</p>
        <div class="tiles">
            <article class="clickable-article">
                <span class="image">
                    <img src="https://via.placeholder.com/300x200/e55100/ffffff?text=Game+1" alt="游戏1">
                </span>
                <header class="major">
                    <h3>游戏 1</h3>
                    <p>响应式测试游戏</p>
                </header>
            </article>
            <article class="clickable-article">
                <span class="image">
                    <img src="https://via.placeholder.com/300x200/ff8a50/ffffff?text=Game+2" alt="游戏2">
                </span>
                <header class="major">
                    <h3>游戏 2</h3>
                    <p>响应式测试游戏</p>
                </header>
            </article>
            <article class="clickable-article">
                <span class="image">
                    <img src="https://via.placeholder.com/300x200/ffa726/ffffff?text=Game+3" alt="游戏3">
                </span>
                <header class="major">
                    <h3>游戏 3</h3>
                    <p>响应式测试游戏</p>
                </header>
            </article>
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">6. 动画测试</div>
        <p>悬停在下面的卡片上查看动画效果：</p>
        <article class="clickable-article" style="max-width: 300px;">
            <span class="image">
                <img src="https://via.placeholder.com/300x200/ff6b35/ffffff?text=Hover+Me" alt="悬停测试">
            </span>
            <header class="major">
                <h3>悬停测试</h3>
                <p>将鼠标悬停在此卡片上</p>
            </header>
        </article>
    </div>

    <script src="assets/js/optimized-ui.js"></script>
    <script>
        // 简单的测试脚本
        document.addEventListener('DOMContentLoaded', function() {
            console.log('样式测试页面已加载');
            
            // 检查CSS变量是否正确加载
            const testElement = document.createElement('div');
            document.body.appendChild(testElement);
            testElement.style.color = 'var(--primary-orange)';
            const computedColor = getComputedStyle(testElement).color;
            
            if (computedColor && computedColor !== 'var(--primary-orange)') {
                console.log('✅ CSS变量正常加载，主要橙色:', computedColor);
            } else {
                console.log('❌ CSS变量加载失败');
            }
            
            document.body.removeChild(testElement);
            
            // 测试交互功能
            const cards = document.querySelectorAll('.clickable-article');
            console.log(`✅ 找到 ${cards.length} 个游戏卡片`);
            
            // 添加测试事件
            cards.forEach((card, index) => {
                card.addEventListener('click', function() {
                    console.log(`卡片 ${index + 1} 被点击`);
                });
            });
        });
    </script>
</body>
</html>
