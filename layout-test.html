<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>布局测试 - Bonk Game</title>
    <link rel="stylesheet" href="assets/css/layout-reset.css">
    <link rel="stylesheet" href="assets/css/optimized-styles.css">
    <style>
        /* 测试页面专用样式 */
        .test-info {
            background: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #ff6b35;
        }
        
        .test-info h3 {
            margin: 0 0 10px 0;
            color: #ff6b35;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .test-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .status-good { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-error { color: #dc3545; }
    </style>
</head>
<body>
    <div id="wrapper">
        <!-- Header -->
        <header id="header">
            <div class="inner">
                <div class="logo">
                    <img src="images/logo.png" alt="Bonk Game" style="width: 40px; height: 40px;">
                    <strong>BONK GAME</strong>
                </div>
            </div>
        </header>

        <!-- Banner -->
        <section id="banner">
            <div class="inner">
                <h1>布局测试页面</h1>
                <p>验证 letsbonk.fun 风格的布局是否正确显示</p>
            </div>
        </section>

        <!-- Main -->
        <div id="main">
            <section id="one">
                <div class="inner">
                    <div class="test-info">
                        <h3>布局状态检查</h3>
                        <div class="test-grid">
                            <div class="test-card">
                                <h4>CSS 加载</h4>
                                <div id="css-status" class="status-warning">检查中...</div>
                            </div>
                            <div class="test-card">
                                <h4>网格布局</h4>
                                <div id="grid-status" class="status-warning">检查中...</div>
                            </div>
                            <div class="test-card">
                                <h4>卡片样式</h4>
                                <div id="card-status" class="status-warning">检查中...</div>
                            </div>
                            <div class="test-card">
                                <h4>响应式</h4>
                                <div id="responsive-status" class="status-warning">检查中...</div>
                            </div>
                        </div>
                    </div>

                    <header class="major">
                        <h2>游戏展示区域</h2>
                        <p>以下是游戏卡片的展示效果，应该呈现为整齐的网格布局</p>
                    </header>

                    <section class="tiles">
                        <article class="clickable-article" onclick="location.href='games/bonk-io.html'">
                            <div class="badge new">NEW</div>
                            <span class="image">
                                <img src="https://via.placeholder.com/400x260/ff6b35/ffffff?text=Bonk.io" alt="Bonk.io">
                            </span>
                            <header class="major">
                                <h3><a href="games/bonk-io.html" class="link">Bonk.io</a></h3>
                                <p>经典的物理碰撞游戏，与朋友一起享受乐趣</p>
                                <div class="game-meta">
                                    <span class="rating">⭐ 4.8</span>
                                    <span class="genre">多人</span>
                                </div>
                            </header>
                        </article>

                        <article class="clickable-article" onclick="location.href='games/bonk-2.html'">
                            <div class="badge">HOT</div>
                            <span class="image">
                                <img src="https://via.placeholder.com/400x260/e55100/ffffff?text=Bonk+2" alt="Bonk 2">
                            </span>
                            <header class="major">
                                <h3><a href="games/bonk-2.html" class="link">Bonk 2</a></h3>
                                <p>升级版本，更多地图和游戏模式</p>
                                <div class="game-meta">
                                    <span class="rating">⭐ 4.9</span>
                                    <span class="genre">动作</span>
                                </div>
                            </header>
                        </article>

                        <article class="clickable-article" onclick="location.href='games/bonk-riders.html'">
                            <span class="image">
                                <img src="https://via.placeholder.com/400x260/ff8a50/ffffff?text=Bonk+Riders" alt="Bonk Riders">
                            </span>
                            <header class="major">
                                <h3><a href="games/bonk-riders.html" class="link">Bonk Riders</a></h3>
                                <p>驾驶载具进行激烈的竞速比赛</p>
                                <div class="game-meta">
                                    <span class="rating">⭐ 4.7</span>
                                    <span class="genre">竞速</span>
                                </div>
                            </header>
                        </article>

                        <article class="clickable-article" onclick="location.href='games/bonk-league.html'">
                            <span class="image">
                                <img src="https://via.placeholder.com/400x260/ffa726/ffffff?text=Bonk+League" alt="Bonk League">
                            </span>
                            <header class="major">
                                <h3><a href="games/bonk-league.html" class="link">Bonk League</a></h3>
                                <p>参加联赛，成为最强的 Bonk 玩家</p>
                                <div class="game-meta">
                                    <span class="rating">⭐ 4.6</span>
                                    <span class="genre">竞技</span>
                                </div>
                            </header>
                        </article>

                        <article class="clickable-article" onclick="location.href='games/bonk-battle.html'">
                            <span class="image">
                                <img src="https://via.placeholder.com/400x260/ff6b35/ffffff?text=Bonk+Battle" alt="Bonk Battle">
                            </span>
                            <header class="major">
                                <h3><a href="games/bonk-battle.html" class="link">Bonk Battle</a></h3>
                                <p>团队对战模式，策略与技巧并重</p>
                                <div class="game-meta">
                                    <span class="rating">⭐ 4.5</span>
                                    <span class="genre">策略</span>
                                </div>
                            </header>
                        </article>

                        <article class="clickable-article" onclick="location.href='games/bonk-adventure.html'">
                            <span class="image">
                                <img src="https://via.placeholder.com/400x260/e55100/ffffff?text=Bonk+Adventure" alt="Bonk Adventure">
                            </span>
                            <header class="major">
                                <h3><a href="games/bonk-adventure.html" class="link">Bonk Adventure</a></h3>
                                <p>单人冒险模式，探索神秘的世界</p>
                                <div class="game-meta">
                                    <span class="rating">⭐ 4.4</span>
                                    <span class="genre">冒险</span>
                                </div>
                            </header>
                        </article>
                    </section>
                </div>
            </section>
        </div>

        <!-- Footer -->
        <footer id="footer">
            <div class="inner">
                <p>&copy; 2024 Bonk Game. 布局测试页面.</p>
            </div>
        </footer>
    </div>

    <!-- Scripts -->
    <script src="assets/js/layout-fix.js"></script>
    <script>
        // 布局状态检查
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                checkLayoutStatus();
            }, 1000);
        });

        function checkLayoutStatus() {
            // 检查 CSS 变量
            const testElement = document.createElement('div');
            document.body.appendChild(testElement);
            testElement.style.color = 'var(--primary-orange)';
            const computedColor = getComputedStyle(testElement).color;
            document.body.removeChild(testElement);

            const cssStatus = document.getElementById('css-status');
            if (computedColor && computedColor !== 'var(--primary-orange)') {
                cssStatus.textContent = '✓ 正常';
                cssStatus.className = 'status-good';
            } else {
                cssStatus.textContent = '✗ 异常';
                cssStatus.className = 'status-error';
            }

            // 检查网格布局
            const tilesContainer = document.querySelector('.tiles');
            const gridStatus = document.getElementById('grid-status');
            if (tilesContainer) {
                const computedStyle = getComputedStyle(tilesContainer);
                if (computedStyle.display === 'grid') {
                    gridStatus.textContent = '✓ 网格布局';
                    gridStatus.className = 'status-good';
                } else {
                    gridStatus.textContent = '✗ 布局异常';
                    gridStatus.className = 'status-error';
                }
            }

            // 检查卡片样式
            const articles = document.querySelectorAll('.tiles article');
            const cardStatus = document.getElementById('card-status');
            if (articles.length > 0) {
                const firstCard = articles[0];
                const computedStyle = getComputedStyle(firstCard);
                if (computedStyle.borderRadius && computedStyle.boxShadow) {
                    cardStatus.textContent = `✓ ${articles.length} 张卡片`;
                    cardStatus.className = 'status-good';
                } else {
                    cardStatus.textContent = '✗ 样式异常';
                    cardStatus.className = 'status-error';
                }
            }

            // 检查响应式
            const responsiveStatus = document.getElementById('responsive-status');
            const screenWidth = window.innerWidth;
            if (screenWidth < 768) {
                responsiveStatus.textContent = '📱 移动端';
                responsiveStatus.className = 'status-good';
            } else if (screenWidth < 1200) {
                responsiveStatus.textContent = '💻 平板端';
                responsiveStatus.className = 'status-good';
            } else {
                responsiveStatus.textContent = '🖥️ 桌面端';
                responsiveStatus.className = 'status-good';
            }
        }

        // 窗口大小改变时重新检查
        window.addEventListener('resize', function() {
            setTimeout(checkLayoutStatus, 100);
        });
    </script>
</body>
</html>
