/**
 * Layout Fix for Bonk Game
 * Ensures proper layout and fixes any remaining style conflicts
 */

(function() {
    'use strict';

    // Wait for DOM to be ready
    document.addEventListener('DOMContentLoaded', function() {
        fixLayout();
        initializeEnhancements();
    });

    function fixLayout() {
        // Fix tiles container
        const tilesContainer = document.querySelector('.tiles');
        if (tilesContainer) {
            // Ensure proper grid layout
            tilesContainer.style.display = 'grid';
            tilesContainer.style.gridTemplateColumns = 'repeat(auto-fill, minmax(300px, 1fr))';
            tilesContainer.style.gap = '32px';
            tilesContainer.style.padding = '0';
            tilesContainer.style.margin = '0';
            tilesContainer.style.border = 'none';
            tilesContainer.style.background = 'none';
            tilesContainer.style.listStyle = 'none';
        }

        // Fix all article cards
        const articles = document.querySelectorAll('.tiles article');
        articles.forEach((article, index) => {
            // Reset article styles
            article.style.display = 'block';
            article.style.position = 'relative';
            article.style.background = 'white';
            article.style.borderRadius = '16px';
            article.style.overflow = 'hidden';
            article.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.12)';
            article.style.transition = 'all 0.3s ease';
            article.style.height = '400px';
            article.style.width = 'auto';
            article.style.margin = '0';
            article.style.padding = '0';
            article.style.border = '1px solid rgba(255, 107, 53, 0.1)';
            article.style.cursor = 'pointer';

            // Add staggered animation
            article.style.animationDelay = `${index * 0.1}s`;
            article.classList.add('fade-in');

            // Fix image container
            const imageContainer = article.querySelector('.image, span.image');
            if (imageContainer) {
                imageContainer.style.position = 'relative';
                imageContainer.style.height = '260px';
                imageContainer.style.overflow = 'hidden';
                imageContainer.style.display = 'block';
                imageContainer.style.margin = '0';
                imageContainer.style.padding = '0';
                imageContainer.style.background = 'linear-gradient(135deg, rgba(255, 107, 53, 0.1), rgba(229, 81, 0, 0.1))';

                // Fix image
                const img = imageContainer.querySelector('img');
                if (img) {
                    img.style.width = '100%';
                    img.style.height = '100%';
                    img.style.objectFit = 'cover';
                    img.style.transition = 'transform 0.3s ease';
                    img.style.display = 'block';
                    img.style.margin = '0';
                    img.style.padding = '0';
                    img.style.border = 'none';
                    img.style.borderRadius = '0';
                }
            }

            // Fix header overlay
            const header = article.querySelector('header.major');
            if (header) {
                header.style.position = 'absolute';
                header.style.bottom = '0';
                header.style.left = '0';
                header.style.right = '0';
                header.style.padding = '24px';
                header.style.background = 'linear-gradient(transparent, rgba(26, 26, 26, 0.95))';
                header.style.color = 'white';
                header.style.backdropFilter = 'blur(8px)';
                header.style.margin = '0';
                header.style.border = 'none';
                header.style.zIndex = '2';

                // Fix title
                const title = header.querySelector('h3');
                if (title) {
                    title.style.fontSize = '1.2rem';
                    title.style.fontWeight = '700';
                    title.style.margin = '0 0 8px 0';
                    title.style.lineHeight = '1.3';
                    title.style.color = 'white';
                    title.style.textShadow = '0 1px 2px rgba(0, 0, 0, 0.5)';
                    title.style.padding = '0';
                    title.style.border = 'none';
                    title.style.background = 'none';

                    const link = title.querySelector('a');
                    if (link) {
                        link.style.color = 'white';
                        link.style.textDecoration = 'none';
                        link.style.transition = 'color 0.15s ease';
                    }
                }

                // Fix description
                const description = header.querySelector('p');
                if (description) {
                    description.style.fontSize = '0.85rem';
                    description.style.opacity = '0.9';
                    description.style.margin = '0 0 4px 0';
                    description.style.color = 'rgba(255, 255, 255, 0.8)';
                    description.style.lineHeight = '1.4';
                    description.style.padding = '0';
                    description.style.border = 'none';
                    description.style.background = 'none';
                }
            }

            // Fix badge
            const badge = article.querySelector('.badge');
            if (badge) {
                badge.style.position = 'absolute';
                badge.style.top = '16px';
                badge.style.left = '16px';
                badge.style.zIndex = '10';
                badge.style.display = 'inline-flex';
                badge.style.alignItems = 'center';
                badge.style.padding = '4px 8px';
                badge.style.background = '#ff6b35';
                badge.style.color = 'white';
                badge.style.borderRadius = '8px';
                badge.style.fontSize = '0.7rem';
                badge.style.fontWeight = '600';
                badge.style.textTransform = 'uppercase';
                badge.style.letterSpacing = '0.05em';
                badge.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
                badge.style.margin = '0';
                badge.style.border = 'none';
            }
        });

        // Fix section spacing
        const mainSection = document.querySelector('#one');
        if (mainSection) {
            mainSection.style.padding = '48px 0';
            mainSection.style.background = '#ffffff';
        }

        // Fix container
        const containers = document.querySelectorAll('.container, .inner');
        containers.forEach(container => {
            container.style.maxWidth = '1400px';
            container.style.margin = '0 auto';
            container.style.padding = '0 24px';
        });
    }

    function initializeEnhancements() {
        // Add hover effects
        const articles = document.querySelectorAll('.tiles article');
        articles.forEach(article => {
            article.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-6px)';
                this.style.boxShadow = '0 20px 40px rgba(255, 107, 53, 0.15)';
                this.style.borderColor = 'rgba(255, 107, 53, 0.3)';

                const img = this.querySelector('.image img, span.image img');
                if (img) {
                    img.style.transform = 'scale(1.08)';
                }
            });

            article.addEventListener('mouseleave', function() {
                this.style.transform = '';
                this.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.12)';
                this.style.borderColor = 'rgba(255, 107, 53, 0.1)';

                const img = this.querySelector('.image img, span.image img');
                if (img) {
                    img.style.transform = '';
                }
            });

            // Add click effect
            article.addEventListener('mousedown', function() {
                this.style.transform = 'translateY(-4px) scale(0.98)';
            });

            article.addEventListener('mouseup', function() {
                this.style.transform = 'translateY(-6px)';
            });
        });

        // Add link hover effects
        const titleLinks = document.querySelectorAll('.tiles article header.major h3 a');
        titleLinks.forEach(link => {
            link.addEventListener('mouseenter', function() {
                this.style.color = '#ff8a50';
            });

            link.addEventListener('mouseleave', function() {
                this.style.color = 'white';
            });
        });

        // Add fade-in animation
        const style = document.createElement('style');
        style.textContent = `
            .fade-in {
                opacity: 0;
                transform: translateY(20px);
                animation: fadeInUp 0.6s ease forwards;
            }
            
            @keyframes fadeInUp {
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);

        // Trigger animations
        setTimeout(() => {
            articles.forEach((article, index) => {
                setTimeout(() => {
                    article.classList.add('fade-in');
                }, index * 100);
            });
        }, 100);
    }

    // Fix any layout issues on window resize
    window.addEventListener('resize', function() {
        setTimeout(fixLayout, 100);
    });

    // Force layout fix after a short delay to ensure all styles are loaded
    setTimeout(fixLayout, 500);

})();
