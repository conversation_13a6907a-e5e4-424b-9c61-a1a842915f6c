/* 
 * Optimized Styles for Bonk Game
 * Inspired by letsbonk.fun design - clean, modern, and efficient
 */

/* CSS Variables */
:root {
  /* Orange Color Palette */
  --primary-orange: #ff6b35;
  --deep-orange: #e55100;
  --light-orange: #ff8a50;
  --golden-orange: #ffa726;
  
  /* Dark theme colors */
  --dark-bg: #1a1a1a;
  --card-bg: #2d2d2d;
  --text-light: rgba(255, 255, 255, 0.9);
  
  /* Spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;
  
  /* Border radius */
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  
  /* Shadows */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.12);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.15);
  --shadow-xl: 0 12px 24px rgba(0, 0, 0, 0.18);
  
  /* Transitions */
  --transition: 0.3s ease;
  --transition-fast: 0.15s ease;
}

/* Base styles */
* {
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: #1a1a1a;
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

/* Header */
#header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 107, 53, 0.1);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 1000;
}

#header .logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) 0;
}

#header .logo img {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
}

#header .logo strong {
  background: linear-gradient(135deg, var(--primary-orange), var(--deep-orange));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 1.25rem;
  font-weight: 700;
}

/* Banner */
#banner {
  background: linear-gradient(135deg, rgba(255, 247, 237, 0.8), transparent);
  padding: var(--spacing-2xl) 0;
  text-align: center;
}

#banner h1 {
  font-size: clamp(2rem, 6vw, 3.5rem);
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: var(--spacing-lg);
  background: linear-gradient(135deg, var(--primary-orange), var(--deep-orange));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

#banner p {
  font-size: 1.125rem;
  color: rgba(0, 0, 0, 0.7);
  margin-bottom: var(--spacing-xl);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Buttons */
.button {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-lg);
  font-weight: 600;
  text-decoration: none;
  transition: var(--transition);
  cursor: pointer;
  border: none;
}

.button.primary {
  background: linear-gradient(135deg, var(--primary-orange), var(--deep-orange));
  color: white;
  box-shadow: var(--shadow-md);
}

.button.primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.button.secondary {
  background: white;
  color: var(--primary-orange);
  border: 2px solid var(--primary-orange);
}

.button.secondary:hover {
  background: rgba(255, 107, 53, 0.1);
  transform: translateY(-2px);
}

/* Actions */
.actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  flex-wrap: wrap;
  margin-top: var(--spacing-xl);
}

.actions li {
  list-style: none;
}

/* Tiles Grid - Clean letsbonk.fun inspired layout */
.tiles {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-xl);
  padding: var(--spacing-2xl) var(--spacing-lg);
  max-width: 1400px;
  margin: 0 auto;
}

/* Tiles Header */
.tiles-header {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
  grid-column: 1 / -1;
  padding: 0 var(--spacing-lg);
}

.tiles-header h2 {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: var(--spacing-md);
  background: linear-gradient(135deg, var(--primary-orange), var(--deep-orange));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.tiles-header p {
  font-size: 1.125rem;
  color: rgba(0, 0, 0, 0.6);
  max-width: 600px;
  margin: 0 auto;
}

/* Game Cards - letsbonk.fun style */
.tiles article {
  background: white;
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: var(--transition);
  position: relative;
  height: 400px;
  cursor: pointer;
  border: 1px solid rgba(255, 107, 53, 0.1);
}

.tiles article:hover {
  transform: translateY(-6px);
  box-shadow: 0 20px 40px rgba(255, 107, 53, 0.15);
  border-color: rgba(255, 107, 53, 0.3);
}

/* Image Container */
.tiles article .image,
.tiles article span.image {
  position: relative;
  height: 260px;
  overflow: hidden;
  background: linear-gradient(135deg, rgba(255, 107, 53, 0.1), rgba(229, 81, 0, 0.1));
}

.tiles article .image img,
.tiles article span.image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition);
  border-radius: 0;
}

.tiles article:hover .image img,
.tiles article:hover span.image img {
  transform: scale(1.08);
}

/* Content Overlay - Dark theme like letsbonk.fun */
.tiles article header.major {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: var(--spacing-lg);
  background: linear-gradient(transparent, rgba(26, 26, 26, 0.95));
  color: white;
  backdrop-filter: blur(8px);
}

.tiles article header.major h3 {
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: var(--spacing-sm);
  line-height: 1.3;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.tiles article header.major h3 a {
  color: white;
  text-decoration: none;
  transition: var(--transition-fast);
}

.tiles article header.major h3 a:hover {
  color: var(--light-orange);
}

.tiles article header.major p {
  font-size: 0.85rem;
  opacity: 0.9;
  margin: 0 0 var(--spacing-xs) 0;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}

/* Badges */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--primary-orange);
  color: white;
  border-radius: var(--radius-md);
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  position: absolute;
  top: var(--spacing-md);
  left: var(--spacing-md);
  z-index: 10;
  box-shadow: var(--shadow-sm);
}

.badge.new {
  background: linear-gradient(135deg, var(--primary-orange), var(--deep-orange));
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

/* Game Meta */
.game-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-xs);
  font-size: 0.75rem;
}

.rating {
  color: #fbbf24;
  font-weight: 600;
}

.genre {
  background: rgba(255, 107, 53, 0.2);
  color: var(--primary-orange);
  padding: 2px var(--spacing-xs);
  border-radius: 4px;
  font-weight: 500;
  font-size: 0.65rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Interactive Elements */
.clickable-article {
  cursor: pointer;
  user-select: none;
}

.clickable-article:active {
  transform: scale(0.98);
}

/* Responsive Design */
@media screen and (min-width: 1200px) {
  .tiles {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: var(--spacing-xl);
  }

  .tiles article {
    height: 400px;
  }
}

@media screen and (max-width: 980px) {
  .tiles {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--spacing-lg);
    padding: var(--spacing-xl) var(--spacing-md);
  }

  .tiles article {
    height: 360px;
  }

  .tiles-header h2 {
    font-size: 2rem;
  }

  #banner h1 {
    font-size: 2.5rem;
  }
}

@media screen and (max-width: 640px) {
  .tiles {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
    padding: var(--spacing-lg) var(--spacing-md);
  }

  .tiles article {
    height: 320px;
    max-width: 400px;
    margin: 0 auto;
  }

  .tiles article header.major {
    padding: var(--spacing-md);
  }

  .tiles article header.major h3 {
    font-size: 1rem;
  }

  .tiles article header.major p {
    font-size: 0.75rem;
  }

  .tiles-header h2 {
    font-size: 1.75rem;
  }

  .tiles-header p {
    font-size: 1rem;
  }

  #banner h1 {
    font-size: 2rem;
  }

  #banner p {
    font-size: 1rem;
  }

  .actions {
    flex-direction: column;
    align-items: center;
  }

  .button {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }
}

/* Section Styles */
section#two {
  background: linear-gradient(135deg, rgba(255, 247, 237, 0.5), transparent);
  padding: var(--spacing-2xl) 0;
  margin-top: var(--spacing-2xl);
}

section#two .inner {
  text-align: center;
}

section#two header.major h2 {
  background: linear-gradient(135deg, var(--primary-orange), var(--deep-orange));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: var(--spacing-lg);
}

section#two p {
  font-size: 1.125rem;
  line-height: 1.6;
  color: rgba(0, 0, 0, 0.7);
  max-width: 800px;
  margin: 0 auto var(--spacing-xl) auto;
}

/* Footer */
#footer {
  background: linear-gradient(135deg, #7c2d12, #9a3412);
  color: white;
  padding: var(--spacing-2xl) 0 var(--spacing-lg);
  margin-top: var(--spacing-2xl);
}

#footer .inner {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
}

#footer h4 {
  color: white;
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: var(--spacing-md);
}

#footer p {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
}

#footer ul {
  list-style: none;
  padding: 0;
}

#footer ul li {
  margin-bottom: var(--spacing-sm);
}

#footer ul li a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: var(--transition);
}

#footer ul li a:hover {
  color: #fed7aa;
  transform: translateX(4px);
}

#footer ul.copyright {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-lg);
  justify-content: center;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding-top: var(--spacing-lg);
  opacity: 0.8;
}

/* Performance Optimizations */
.tiles article img {
  will-change: transform;
}

.tiles article:hover img {
  will-change: auto;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus States */
.tiles article:focus-visible,
.button:focus-visible {
  outline: 2px solid var(--primary-orange);
  outline-offset: 2px;
}

/* Animation Classes */
.animate-in {
  animation: slideInUp 0.6s ease forwards;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Skip Link for Accessibility */
.skip-link:focus {
  top: 6px !important;
}

/* Loading States */
.loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Enhanced Header States */
#header.scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: var(--shadow-lg);
}

/* Print Styles */
@media print {
  .tiles article {
    break-inside: avoid;
    page-break-inside: avoid;
  }

  .badge,
  .button,
  #header nav {
    display: none;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .tiles article {
    border: 2px solid #000;
  }

  .badge {
    border: 1px solid #000;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  :root {
    --text-color: #ffffff;
    --bg-color: #1a1a1a;
  }

  body {
    background-color: var(--bg-color);
    color: var(--text-color);
  }

  .tiles article {
    background: var(--card-bg);
    color: var(--text-light);
  }

  #header {
    background: rgba(26, 26, 26, 0.95);
    border-bottom-color: rgba(255, 107, 53, 0.3);
  }
}
