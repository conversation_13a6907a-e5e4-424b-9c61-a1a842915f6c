.loading-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(36, 41, 52, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-background {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    background: linear-gradient(45deg, #8b4513, #ffffff);
    animation: rotate 3s linear infinite;
}

@keyframes rotate {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.loading-text {
    font-size: 24px;
    opacity: 0;
    animation: fadeIn 5s forwards;
    color: #8b4513;
    margin-top: 20px;
}

@keyframes fadeIn {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}
