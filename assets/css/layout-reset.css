/* 
 * Layout Reset for Bonk Game
 * Fixes conflicts and ensures clean layout
 */

/* Reset conflicting styles */
* {
  box-sizing: border-box;
}

/* Body and main container reset */
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: #1a1a1a;
  background: #ffffff;
}

/* Main wrapper */
#wrapper {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* Header reset */
#header {
  position: sticky;
  top: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 107, 53, 0.1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

#header .inner {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

/* Banner section */
#banner {
  background: linear-gradient(135deg, rgba(255, 247, 237, 0.8), transparent);
  padding: 80px 0;
  text-align: center;
}

#banner .inner {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

#banner h1 {
  font-size: clamp(2rem, 6vw, 3.5rem);
  font-weight: 800;
  line-height: 1.1;
  margin: 0 0 24px 0;
  background: linear-gradient(135deg, #ff6b35, #e55100);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

#banner p {
  font-size: 1.125rem;
  color: rgba(0, 0, 0, 0.7);
  margin: 0 0 32px 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Main content area */
#main {
  flex: 1;
  padding: 0;
}

/* Section reset */
section {
  margin: 0;
  padding: 0;
}

section .inner {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
}

/* Tiles section specific */
#one {
  padding: 48px 0;
  background: #ffffff;
}

/* Clear any conflicting tile styles */
.tiles {
  display: grid !important;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)) !important;
  gap: 32px !important;
  padding: 0 !important;
  margin: 0 !important;
  border: none !important;
  background: none !important;
  list-style: none !important;
}

/* Article reset */
.tiles article {
  display: block !important;
  position: relative !important;
  background: white !important;
  border-radius: 16px !important;
  overflow: hidden !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12) !important;
  transition: all 0.3s ease !important;
  height: 400px !important;
  width: auto !important;
  margin: 0 !important;
  padding: 0 !important;
  border: 1px solid rgba(255, 107, 53, 0.1) !important;
  cursor: pointer !important;
}

.tiles article:hover {
  transform: translateY(-6px) !important;
  box-shadow: 0 20px 40px rgba(255, 107, 53, 0.15) !important;
  border-color: rgba(255, 107, 53, 0.3) !important;
}

/* Image container reset */
.tiles article .image,
.tiles article span.image {
  position: relative !important;
  height: 260px !important;
  overflow: hidden !important;
  display: block !important;
  margin: 0 !important;
  padding: 0 !important;
  background: linear-gradient(135deg, rgba(255, 107, 53, 0.1), rgba(229, 81, 0, 0.1)) !important;
}

.tiles article .image img,
.tiles article span.image img {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  transition: transform 0.3s ease !important;
  display: block !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  border-radius: 0 !important;
}

.tiles article:hover .image img,
.tiles article:hover span.image img {
  transform: scale(1.08) !important;
}

/* Header overlay reset */
.tiles article header.major {
  position: absolute !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  padding: 24px !important;
  background: linear-gradient(transparent, rgba(26, 26, 26, 0.95)) !important;
  color: white !important;
  backdrop-filter: blur(8px) !important;
  margin: 0 !important;
  border: none !important;
  z-index: 2 !important;
}

.tiles article header.major h3 {
  font-size: 1.2rem !important;
  font-weight: 700 !important;
  margin: 0 0 8px 0 !important;
  line-height: 1.3 !important;
  color: white !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
  padding: 0 !important;
  border: none !important;
  background: none !important;
}

.tiles article header.major h3 a {
  color: white !important;
  text-decoration: none !important;
  transition: color 0.15s ease !important;
}

.tiles article header.major h3 a:hover {
  color: #ff8a50 !important;
}

.tiles article header.major p {
  font-size: 0.85rem !important;
  opacity: 0.9 !important;
  margin: 0 0 4px 0 !important;
  color: rgba(255, 255, 255, 0.8) !important;
  line-height: 1.4 !important;
  padding: 0 !important;
  border: none !important;
  background: none !important;
}

/* Badge reset */
.badge {
  position: absolute !important;
  top: 16px !important;
  left: 16px !important;
  z-index: 10 !important;
  display: inline-flex !important;
  align-items: center !important;
  padding: 4px 8px !important;
  background: #ff6b35 !important;
  color: white !important;
  border-radius: 8px !important;
  font-size: 0.7rem !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.05em !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  margin: 0 !important;
  border: none !important;
}

/* Footer reset */
#footer {
  background: linear-gradient(135deg, #7c2d12, #9a3412);
  color: white;
  padding: 48px 0 24px;
  margin-top: auto;
}

#footer .inner {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
}

/* Actions reset */
.actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
  margin: 32px 0 0 0;
  padding: 0;
  list-style: none;
}

.actions li {
  list-style: none;
  margin: 0;
  padding: 0;
}

/* Button reset */
.button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 16px 24px;
  border-radius: 12px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
  font-size: 1rem;
}

.button.primary {
  background: linear-gradient(135deg, #ff6b35, #e55100);
  color: white;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
}

.button.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

/* Clear any float issues */
.clearfix::after {
  content: "";
  display: table;
  clear: both;
}

/* Ensure proper stacking */
.tiles article .badge {
  z-index: 20 !important;
}

.tiles article .image::after {
  content: none !important;
}

/* Remove any unwanted borders or backgrounds */
.tiles article * {
  box-sizing: border-box;
}

/* Responsive adjustments */
@media screen and (max-width: 1200px) {
  .tiles {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)) !important;
    gap: 24px !important;
  }
}

@media screen and (max-width: 768px) {
  .tiles {
    grid-template-columns: 1fr !important;
    gap: 20px !important;
  }
  
  .tiles article {
    height: 350px !important;
    max-width: 400px !important;
    margin: 0 auto !important;
  }
  
  .tiles article .image,
  .tiles article span.image {
    height: 220px !important;
  }
}
