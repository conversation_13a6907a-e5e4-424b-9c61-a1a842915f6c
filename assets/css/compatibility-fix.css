/*
 * Optimized CSS for Bonk Game
 * Clean, modern styles inspired by letsbonk.fun
 */

/* CSS Variables for consistent theming */
:root {
  /* Orange Color Palette - inspired by letsbonk.fun */
  --primary-orange: #ff6b35;
  --deep-orange: #e55100;
  --light-orange: #ff8a50;
  --golden-orange: #ffa726;

  /* Dark theme colors */
  --dark-bg: #1a1a1a;
  --card-bg: #2d2d2d;
  --card-hover-bg: #3a3a3a;

  /* Spacing system */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;

  /* Border radius */
  --border-radius: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;

  /* Shadows */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.12);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.15);
  --shadow-xl: 0 12px 24px rgba(0, 0, 0, 0.18);

  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* Clean tiles layout */
.tiles {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--spacing-lg);
  padding: var(--spacing-2xl) 0;
  max-width: 1200px;
  margin: 0 auto;
  border-top: none;
}

/* Modern card design */
.tiles article {
  background: white;
  border-radius: var(--border-radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: var(--transition-normal);
  position: relative;
  height: 380px;
  cursor: pointer;
  display: block;
  width: 100%;
}

.tiles article:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

/* Image container styling */
.tiles article .image,
.tiles article span.image {
  position: relative;
  height: 240px;
  overflow: hidden;
  display: block;
}

.tiles article .image img,
.tiles article span.image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition-slow);
  display: block;
}

.tiles article:hover .image img,
.tiles article:hover span.image img {
  transform: scale(1.05);
}

/* Content overlay */
.tiles article header.major {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: var(--spacing-lg);
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.85));
  color: white;
  height: auto;
  margin: 0;
}

.tiles article header.major h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
  line-height: 1.3;
  color: white;
}

.tiles article header.major h3 a {
  color: white;
  text-decoration: none;
}

.tiles article header.major p {
  font-size: 0.8rem;
  opacity: 0.9;
  margin: 0 0 var(--spacing-xs) 0;
  color: white;
}

/* Badge styling */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--primary-orange);
  color: white;
  border-radius: var(--border-radius);
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  position: absolute;
  top: var(--spacing-md);
  left: var(--spacing-md);
  z-index: 10;
  box-shadow: var(--shadow-sm);
}

.badge.new {
  background: linear-gradient(135deg, var(--primary-orange), var(--deep-orange));
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

/* Game meta information */
.game-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-xs);
  font-size: 0.75rem;
}

.rating {
  color: #fbbf24;
  font-weight: 600;
}

.genre {
  background: rgba(255, 107, 53, 0.2);
  color: var(--primary-orange);
  padding: 2px var(--spacing-xs);
  border-radius: 4px;
  font-weight: 500;
  font-size: 0.65rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Responsive design */
@media screen and (min-width: 1200px) {
  .tiles {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: var(--spacing-xl);
  }

  .tiles article {
    height: 400px;
  }
}

@media screen and (max-width: 980px) {
  .tiles {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--spacing-lg);
    padding: var(--spacing-xl) var(--spacing-md);
  }

  .tiles article {
    height: 360px;
  }
}

@media screen and (max-width: 640px) {
  .tiles {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
    padding: var(--spacing-lg) var(--spacing-md);
  }

  .tiles article {
    height: 320px;
    max-width: 400px;
    margin: 0 auto;
  }

  .tiles article header.major {
    padding: var(--spacing-md);
  }

  .tiles article header.major h3 {
    font-size: 1rem;
  }

  .tiles article header.major p {
    font-size: 0.75rem;
  }
}

/* Container and layout */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

/* Section spacing */
#one.tiles {
  padding: var(--spacing-2xl) 0;
}

/* Tiles header */
.tiles-header {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
  grid-column: 1 / -1;
  width: 100%;
}

.tiles-header h2 {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: var(--spacing-md);
  background: linear-gradient(135deg, var(--primary-orange), var(--deep-orange));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.tiles-header p {
  font-size: 1.125rem;
  color: rgba(0, 0, 0, 0.6);
  max-width: 600px;
  margin: 0 auto;
}

/* Interactive elements */
.clickable-article {
  cursor: pointer;
  user-select: none;
}

.clickable-article:active {
  transform: scale(0.98);
}

/* Header and navigation */
#header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 107, 53, 0.1);
  box-shadow: var(--shadow-sm);
  transition: var(--transition-normal);
}

#header .logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
}

#header .logo img {
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
}

#header .logo strong {
  background: linear-gradient(135deg, var(--primary-orange), var(--deep-orange));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 1.25rem;
  font-weight: 700;
}

/* Banner section */
#banner {
  background: linear-gradient(135deg, rgba(255, 247, 237, 0.8), transparent, rgba(255, 247, 237, 0.8));
  padding: var(--spacing-2xl) 0;
  min-height: 60vh;
  display: flex;
  align-items: center;
}

#banner h1 {
  font-size: clamp(2rem, 6vw, 3.5rem);
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: var(--spacing-lg);
  background: linear-gradient(135deg, var(--primary-orange), var(--deep-orange));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-align: center;
}

/* Performance optimizations */
.tiles article img {
  will-change: transform;
}

.tiles article:hover img {
  will-change: auto;
}