<!DOCTYPE html>
<html lang="en">

<!-- Mirrored from bonkgame.com/random by HTTrack Website Copier/3.x [XR&CO'2014], Wed, 09 Jul 2025 09:03:07 GMT -->
<!-- Added by HTTrack --><meta http-equiv="content-type" content="text/html;charset=utf-8" /><!-- /Added by HTTrack -->
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Random Game Open</title>
    <script src="assets/js/game-pages.js"></script> <!-- Load the dynamically generated game list -->
    <script>
        // Function to open a random game in a new tab
        function openRandomGame() {
            const randomIndex = Math.floor(Math.random() * gamePages.length);
            const selectedGame = gamePages[randomIndex];

            console.log('Opening game:', selectedGame); // Debugging output

            // Open the selected game in a new tab
            window.open(selectedGame, '_self');
        }

        // Open a random game when the page loads
        window.onload = openRandomGame;
    </script>
</head>
<body>
    <p>Opening a random game...</p>
</body>

<!-- Mirrored from bonkgame.com/random by HTTrack Website Copier/3.x [XR&CO'2014], Wed, 09 Jul 2025 09:03:08 GMT -->
</html>

